// store/destinationListStore.js
import { create } from "zustand";

const useDestinationListStore = create((set) => ({
  destinations: [],
  loading: false,
  error: null,
  pageNumber: 1,
  pageSize: 10,
  totalPages: 1,

  fetchDestinations: async (page = 1, size = 10) => {
    set({ loading: true, error: null, pageNumber: page, pageSize: size });
    try {
      const response = await fetch(
        `${import.meta.env.VITE_API_BASE_URL}/api/destinations?pageSize=${size}&pageNumber=${page}`
      );
      if (!response.ok) {
        throw new Error(`Failed to fetch destinations: ${response.status}`);
      }
      const data = await response.json();
      set({
        destinations: data.destinations || [],
        totalPages: data.paginationInfo?.totalPages || 1,
        loading: false,
      });
    } catch (error) {
      set({ error: error.message, loading: false });
    }
  },

  clearDestinations: () => set({ destinations: [], error: null }),
}));

export default useDestinationListStore;
