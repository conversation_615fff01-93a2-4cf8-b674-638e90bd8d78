import { useState, useCallback, useEffect, useRef } from "react";
import { FiImage, FiX, FiChevronLeft, FiChevronRight } from "react-icons/fi";
import constructUrl from "../../../../utils/constructUrl";

function Gallery({ property }) {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [mainImageLoaded, setMainImageLoaded] = useState(false);

  // Keep track of which images have already loaded to avoid showing skeleton again
  const loadedImages = useRef(new Set());

  const openModal = (index) => {
    setSelectedImageIndex(index);
    setIsModalOpen(true);
  };

  const closeModal = () => setIsModalOpen(false);

  const nextImage = useCallback(() => {
    setSelectedImageIndex((prev) =>
      prev === property.gallery.length - 1 ? 0 : prev + 1
    );
  }, [property.gallery]);

  const prevImage = useCallback(() => {
    setSelectedImageIndex((prev) =>
      prev === 0 ? property.gallery.length - 1 : prev - 1
    );
  }, [property.gallery]);

  // When selected image changes, check if it's loaded; show skeleton only if not
  useEffect(() => {
    if (loadedImages.current.has(selectedImageIndex)) {
      setMainImageLoaded(true);
    } else {
      setMainImageLoaded(false);
    }
  }, [selectedImageIndex]);

  useEffect(() => {
    const handleKeyPress = (e) => {
      if (!isModalOpen) return;
      if (e.key === "Escape") closeModal();
      if (e.key === "ArrowRight") nextImage();
      if (e.key === "ArrowLeft") prevImage();
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [isModalOpen, nextImage, prevImage]);

  if (!property?.gallery?.length) return null;

  return (
    <>
      <section className="mb-12">
        <div className="bg-white rounded-2xl shadow-lg p-6 md:p-8">
          <div className="flex items-center gap-3 mb-6">
            <FiImage className="text-3xl text-primary" />
            <h3 className="text-2xl font-bold text-primary">
              Property Gallery ({property.gallery.length} images)
            </h3>
          </div>

          {/* Main Image Container */}
          <div className="relative mb-6 min-h-[400px] bg-gray-100 rounded-xl shadow-md overflow-hidden cursor-pointer">
            {/* Skeleton placeholder */}
            {!mainImageLoaded && (
              <div className="absolute inset-0 animate-pulse bg-gray-300" />
            )}

            <img
              key={selectedImageIndex} // force remount when image changes
              src={constructUrl(property.gallery[selectedImageIndex])}
              alt={`Gallery image ${selectedImageIndex + 1}`}
              className={`w-full h-[400px] object-cover rounded-xl shadow-md transition-transform duration-300 hover:scale-[1.02] ${
                mainImageLoaded ? "block" : "hidden"
              }`}
              onClick={() => openModal(selectedImageIndex)}
              loading="lazy"
              onLoad={() => {
                loadedImages.current.add(selectedImageIndex);
                setMainImageLoaded(true);
              }}
            />

            <div className="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm select-none">
              {selectedImageIndex + 1} / {property.gallery.length}
            </div>
          </div>

          {/* Thumbnails */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
            {property.gallery.slice(0, 8).map((img, index) => (
              <button
                key={index}
                onClick={() => setSelectedImageIndex(index)}
                className={`rounded-lg overflow-hidden transition-all border-2 focus:outline-none ${
                  selectedImageIndex === index
                    ? "border-primary shadow"
                    : "border-transparent hover:border-primary/50"
                }`}
                aria-label={`Select image ${index + 1}`}
              >
                <img
                  src={constructUrl(img)}
                  alt={`Thumbnail ${index + 1}`}
                  className="w-full h-20 sm:h-24 object-cover"
                  loading="lazy"
                />
              </button>
            ))}

            {property.gallery.length > 8 && (
              <button
                onClick={() => openModal(8)}
                className="relative flex items-center justify-center rounded-xl overflow-hidden group focus:outline-none focus-visible:ring-2 focus-visible:ring-primary/60"
                aria-label={`View ${property.gallery.length - 8} more images`}
              >
                {/* Stacked thumbnails */}
                <div className="relative w-full h-20 sm:h-24">
                  {/* Back layer */}
                  <img
                    src={constructUrl(property.gallery[8])}
                    alt="Extra thumbnail"
                    className="absolute inset-0 w-full h-full object-cover rounded-xl transform translate-x-1 translate-y-1 scale-95 opacity-60"
                    loading="lazy"
                  />
                  {/* Middle layer */}
                  <img
                    src={constructUrl(
                      property.gallery[9] || property.gallery[0]
                    )}
                    alt="Extra thumbnail"
                    className="absolute inset-0 w-full h-full object-cover rounded-xl transform translate-x-0.5 translate-y-0.5 scale-[0.98] opacity-80"
                    loading="lazy"
                  />
                  {/* Foreground layer with overlay */}
                  <div className="absolute inset-0 bg-black/50 flex flex-col items-center justify-center text-white rounded-xl group-hover:bg-black/60 transition-all duration-300">
                    <span className="text-lg sm:text-xl font-bold">
                      + {property.gallery.length - 8} 
                    </span>
                    <span className="text-md opacity-80">View all</span>
                  </div>
                </div>
              </button>
            )}
          </div>
        </div>
      </section>

      {/* Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4">
          <div className="relative w-full max-w-6xl max-h-full flex items-center justify-center">
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 z-10 bg-white/10 hover:bg-white/20 text-white p-2 rounded-full focus:outline-none"
              aria-label="Close modal"
            >
              <FiX className="text-2xl" />
            </button>

            {property.gallery.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-white/10 hover:bg-white/20 text-white p-2 rounded-full focus:outline-none"
                  aria-label="Previous image"
                >
                  <FiChevronLeft className="text-2xl" />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-white/10 hover:bg-white/20 text-white p-2 rounded-full focus:outline-none"
                  aria-label="Next image"
                >
                  <FiChevronRight className="text-2xl" />
                </button>
              </>
            )}

            <img
              key={selectedImageIndex} // force remount in modal as well
              src={constructUrl(property.gallery[selectedImageIndex])}
              alt={`Gallery image ${selectedImageIndex + 1}`}
              className="max-w-full max-h-[80vh] object-contain rounded-lg"
              loading="lazy"
            />

            {/* Modal Counter */}
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/70 text-white px-4 py-1 rounded-full text-sm select-none">
              {selectedImageIndex + 1} / {property.gallery.length}
            </div>

            {/* Thumbnail Strip */}
            <div className="absolute bottom-20 left-1/2 -translate-x-1/2 flex gap-2 px-4 overflow-x-auto max-w-full">
              {property.gallery.map((img, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImageIndex(index)}
                  className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden focus:outline-none ${
                    selectedImageIndex === index
                      ? "ring-2 ring-white"
                      : "opacity-60 hover:opacity-90"
                  }`}
                  aria-label={`Preview image ${index + 1}`}
                >
                  <img
                    src={constructUrl(img)}
                    alt={`Thumbnail preview ${index + 1}`}
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default Gallery;
