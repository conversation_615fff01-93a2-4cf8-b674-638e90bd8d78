.heroSection {
  position: relative;
  height: clamp(40rem, 100vh, 120rem);
  width: 100%;
  overflow: hidden;
  min-height: 40rem;
  --left-margin: clamp(1rem, 10.5vw, 20.1rem);
  /* --content-width: clamp(300px, 90vw, 109.5rem); */
  --content-width: clamp(300px, 90vw, 139.5rem);
  --subtitle-width: clamp(280px, 80vw, 49.1rem);
  /* --title-top: clamp(15rem, 70vh, 69.6rem);
  --subtitle-top: clamp(25rem, 88.7vh, 86.2rem); */
   --title-top:70vh;
     --subtitle-top:88.7vh;
}

.heroImageContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.heroImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.heroContent {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  padding: 0 clamp(0.5rem, 2vw, 2rem);
  box-sizing: border-box;
}

.heroTitle {
  position: absolute;
  width: var(--content-width);
  top: var(--title-top);
  left: var(--left-margin);
  font-family: "Playfair Display", serif;
  font-weight: 700;
  font-size: clamp(1.8rem, 4vw, 7.533rem);
  /* line-height: clamp(2.2rem, 3.8vw, 6.8rem); */
  line-height: 1;
  letter-spacing: 0%;
  color: #ffffff;
  margin: 0;
  text-align: left;
}

.heroSubtitle {
  position: absolute;
  font-size: clamp(1rem, 1.25vw, 2.4rem);
  width: var(--subtitle-width);
  top: var(--subtitle-top);
  left: var(--left-margin);
  color: #ffffff;
  margin: 0;
  line-height: 1.4;
  text-align: left;
}

.heroSection::before {
  content: "";
  position: absolute;
  top: 0;
  left: -4px;
  width: 100vw;
  height: 46.1vh;
  background: linear-gradient(
    180deg,
    #ffffff 18.47%,
    rgba(255, 255, 255, 0) 77.25%
  );
  opacity: 0.42;
  z-index: 1;
}

.heroSection::after {
  content: "";
  position: absolute;
  top: 43.5vh;
  left: 0;
  width: 100%;
  height: 57.3vh;
  background: linear-gradient(
    180deg,
    rgba(8, 14, 22, 0) 38.01%,
    rgba(10, 15, 22, 0.68) 59.27%,
    rgba(14, 16, 20, 0.68) 100%
  );
  z-index: 1;
}
@media (max-width: 1600px) {
  .heroTitle{
    font-size: 5rem ;
  }
  .heroSubtitle{
    font-size: 2rem;
  }
}

@media (max-width: 900px){
    .heroTitle{
    font-size: 4rem;
    line-height: 1.5;
  }
  .heroSubtitle{
    /* font-size: 1.5rem; */
  }
}
@media (max-width: 700px){
    .heroTitle{
    font-size: 3.5rem;
    /* line-height: 1; */
  }
  .heroSubtitle{
    font-size: 1.5rem;
  }
}
@media (max-width: 620px){
    .heroTitle{
    font-size: 3rem;
  }
  .heroSubtitle{
    font-size: 1.5rem;
  }
}
@media (max-width: 528px){
  .heroSection {
   --title-top:70vh;
     --subtitle-top:80vh;
}
    .heroTitle{
    font-size: 2.7rem;
  }
  .heroSubtitle{
    font-size: 1.4rem;
  }
}
@media (max-width: 470px){
    .heroTitle{
    font-size: 2.3rem;
  }
  .heroSubtitle{
    font-size: 1.2rem;
  }
}
@media (max-width: 390px){
    .heroTitle{
    font-size: 2rem;
  }
  .heroSubtitle{
    font-size: 1.2rem;
  }
}

