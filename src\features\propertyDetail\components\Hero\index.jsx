import { useState, useEffect } from "react";
import constructUrl from "../../../../utils/constructUrl";

function Hero({ property }) {
  const [imageLoaded, setImageLoaded] = useState(false);

  // Use the projectHero if available, otherwise mainImage
  const heroImage = property.mainImage;

  // When property or heroImage changes, reset loading state


  if (!property) return null;
  return (
    <div className="w-full h-[65vh] relative flex items-end mb-10 bg-gray-300 overflow-hidden rounded-lg">
      {/* Skeleton placeholder */}
      {!imageLoaded && (
        <div className="absolute inset-0 animate-pulse bg-gray-400 z-0" />
      )}

      <img
        src={constructUrl(heroImage)}
        alt={property.title}
        loading="lazy"
        onLoad={() => setImageLoaded(true)}
        className={`w-full h-full object-cover absolute inset-0 z-0 transition-opacity duration-700 ${
          imageLoaded ? "opacity-100" : "opacity-0"
        }`}
      />

      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent z-10" />
      <div className="relative z-20 px-8 pb-10 max-w-4xl mx-auto w-full">
        <h1 className="text-5xl font-extrabold text-white mb-2 drop-shadow-lg">
          {property.title}
        </h1>
        {property.subtitle && (
          <h2 className="text-2xl md:text-3xl text-primary font-semibold italic mb-2 drop-shadow-lg">
            {property.subtitle}
          </h2>
        )}
        {property.summary && (
          <p className="text-xl md:text-2xl text-white font-medium drop-shadow mb-2 max-w-2xl">
            {property.summary}
          </p>
        )}
      </div>
    </div>
  );
}

export default Hero;
