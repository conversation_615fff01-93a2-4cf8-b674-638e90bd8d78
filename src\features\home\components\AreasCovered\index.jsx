import { useEffect } from "react";
import Card from "../../../../components/Card/Card";
import Subtitle from "../../../../components/Subtitle";
import { Link } from "react-router-dom";
import useDestinationListStore from "../../../destination/store/destinationListStore";
import constructUrl from "../../../../utils/constructUrl";

function AreasCovered() {
  const {
    destinations,
    loading,
    error,
    pageNumber,
    totalPages,
    fetchDestinations,
  } = useDestinationListStore();

  useEffect(() => {
    fetchDestinations(1, 20); // Fetch first page with 20 items
  }, [fetchDestinations]);

  return (
    <section className="py-20 bg-secondary text-white">
      <div className="mx-auto px-6 md:px-30">
        <Subtitle text="AREA THAT WE COVER" />
        <div className="grid md:grid-cols-12 gap-5">
          <h2 className="md:col-span-8 text-6xl leading-tight">
            If you're looking to invest in off plan <br /> or secondary market,
            <span className="pl-4 text-primary italic font-bold">
              we can help you!
            </span>
          </h2>
          <div className="md:col-span-4 justify-self-end">
            <Link
              to="/destinations"
              className="text-lg text-[#bea15d] cursor-pointer"
            >
              <span className="underline underline-offset-3">All cities</span>
              <span className="font-extrabold ml-3">→</span>
            </Link>
          </div>
        </div>

        {/* Show loading / error states */}
        {loading && <p className="mt-8 text-center">Loading destinations...</p>}
        {error && (
          <p className="mt-8 text-center text-red-400">
            Error loading destinations: {error}
          </p>
        )}

        {/* Destinations Grid */}
        <div className="grid md:grid-cols-2 gap-8 mt-10">
          {!loading &&
            !error &&
            destinations.map((dest) => (
              <Link
                key={dest._id}
                to={`/destinations?destination=${encodeURIComponent(dest._id)}`}
              >
                <Card
                  title={`${dest.name}`}
                  subtitle={`from ${dest.from.toLocaleString()} AED`}
                  imgSrc={`${constructUrl(dest.img)}`}
                />
              </Link>
            ))}
        </div>

        {/* Pagination navigation if needed */}
        {totalPages > 1 && (
          <div className="mt-12 flex justify-center gap-4">
            {Array.from({ length: totalPages }).map((_, idx) => {
              const page = idx + 1;
              return (
                <button
                  key={page}
                  className={`px-4 py-2 rounded ${
                    page === pageNumber
                      ? "bg-primary text-white"
                      : "bg-gray-700 text-gray-300"
                  }`}
                  onClick={() => fetchDestinations(page, 10)}
                  disabled={loading}
                >
                  {page}
                </button>
              );
            })}
          </div>
        )}
      </div>
    </section>
  );
}

export default AreasCovered;
