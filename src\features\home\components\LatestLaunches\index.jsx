import { useEffect, useState } from "react";
import Subtitle from "../../../../components/Subtitle";
import useProjectStore from "../../../destination/store/destinationStore";

import constructUrl from "../../../../utils/constructUrl";
import { useNavigate } from "react-router-dom";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import useDeveloperStore from "../../../destination/store/developerStore";

function LatestLaunches() {
  const { projects, fetchProjects, loading } = useProjectStore();
  const { developers, fetchDevelopers } = useDeveloperStore(); 
  const [imgLoadError, setImgLoadError] = useState({});
  const navigate = useNavigate();

  useEffect(() => {
    fetchProjects({}, 10);
    fetchDevelopers(); // ✅ Fetch developer names on load
  }, [fetchProjects, fetchDevelopers]);

  const handleClick = (id) => {
    navigate(`/projects/${id}`);
  };

  const handleImgError = (id) => {
    setImgLoadError((prev) => ({ ...prev, [id]: true }));
  };

  const latestProjects = [...projects].slice(-10).reverse();

  const sliderSettings = {
    infinite: true,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    autoplay: true,
    pauseOnHover: false,
    autoplaySpeed: 3000,
    cssEase: "ease",
    arrows: false,
    swipeToSlide: true,
    draggable: true,
    accessibility: true,
    responsive: [
      { breakpoint: 1024, settings: { slidesToShow: 3 } },
      { breakpoint: 640, settings: { slidesToShow: 1 } },
    ],
  };

  return (
    <section className="w-full mx-auto bg-secondary text-white py-20 lg:px-0 overflow-hidden">
      <div className="mx-auto px-6 md:px-50">
        <h2 className="text-5xl text-center mx-auto font-medium mb-10">
          Discover a{" "}
          <span className="text-primary italic font-semibold">
            Premium Selection
          </span>
          <br />
          of our properties and Exclusive Deals
        </h2>
        <Subtitle text="Latest Launches" />
      </div>

      {/* Carousel */}
      <div className="mt-12 min-h-[20rem]">
        {loading ? (
          <p className="text-center text-lg">Loading latest projects...</p>
        ) : latestProjects.length === 0 ? (
          <p className="text-center text-lg">
            No projects available at the moment.
          </p>
        ) : (
          <Slider {...sliderSettings} aria-label="Latest projects carousel">
            {latestProjects.map((project) => {
              const hasError = imgLoadError[project._id];
              return (
                <div
                  key={project._id}
                  className="px-3 cursor-pointer outline-none"
                  role="button"
                  tabIndex={0}
                  aria-describedby={`desc-${project._id}`}
                  onClick={() => handleClick(project._id)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      handleClick(project._id);
                    }
                  }}
                >
                  <div className="group relative rounded-xl overflow-hidden shadow-lg transform transition-transform duration-300 ease-in-out hover:scale-105">
                    {!hasError ? (
                      <img
                        src={constructUrl(project.mainImage)}
                        alt={project.title}
                        className="object-cover w-full h-60 sm:h-72 lg:h-80 rounded-t-xl"
                        loading="lazy"
                        onError={() => handleImgError(project._id)}
                      />
                    ) : (
                      <div className="bg-gray-700 flex items-center justify-center h-60 sm:h-72 lg:h-80 w-full rounded-t-xl">
                        <p className="text-gray-400 italic text-center px-3">
                          Image not available
                        </p>
                      </div>
                    )}
                    <div
                      className="relative rounded-b-xl overflow-hidden"
                      title={project.title}
                      id={`desc-${project._id}`}
                    >
                      <h3 className="relative z-10 text-center text-white text-base sm:text-xl font-semibold px-4 py-3 line-clamp-2 break-words">
                        {project.title}
                      </h3>
                    </div>
                    <div
                      className="absolute inset-0 flex items-center justify-center text-white text-2xl font-semibold opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none select-none"
                      style={{ backgroundColor: "rgba(0, 0, 0, 0.5)" }}
                    >
                      See Details
                    </div>
                  </div>
                </div>
              );
            })}
          </Slider>
        )}
      </div>
      {/* Companies Row — now dynamic */}
      <div className="mt-20 w-full flex flex-wrap gap-8 lg:flex-nowrap justify-between items-center text-gray-400 gap-y-4">
        <p className="whitespace-nowrap text-2xl pl-55">
          Trusted by
          <span className="pl-2 font-semibold text-white">150+ companies</span>
          <br />
          from startups to enterprises:
        </p>
        <ScrollingBanner
  companies={developers.map((dev) => dev.name.toUpperCase())}
          speed={15}
        />
      </div>
    </section>
  );
}

export default LatestLaunches;

const ScrollingBanner = ({ companies, speed = 30 }) => {
  const shouldScroll = companies.length > 4; // ✅ Only scroll if more than 4

  return (
    <div className="banner-container font-sans">
      <div
        className="banner-content"
        style={{
          "--duration": `${speed}s`,
          animation: shouldScroll ? undefined : "none", // Disable scroll
        }}
      >
        <div className="banner-items">
          {companies.map((company, index) => (
            <span key={`original-${index}`} className="banner-item">
              {company}
            </span>
          ))}
        </div>

        {shouldScroll && (
          <div className="banner-items" aria-hidden="true">
            {companies.map((company, index) => (
              <span key={`duplicate-${index}`} className="banner-item">
                {company}
              </span>
            ))}
          </div>
        )}
      </div>

      <style jsx>{`
        .banner-container {
          width: 100%;
          overflow: hidden;
          padding: 2rem 0;
        }
        .banner-content {
          display: flex;
          animation: scroll var(--duration, 20s) linear infinite;
          width: max-content;
        }
        .banner-items {
          display: flex;
          gap: 5rem;
          white-space: nowrap;
        }
        .banner-item {
          letter-spacing: 0.3rem;
          color: white;
          font-weight: 600;
          font-size: 1rem;
          flex-shrink: 0;
        }
        @media (min-width: 1024px) {
          .banner-item {
            font-size: 1.875rem;
          }
        }
        @keyframes scroll {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }
        .banner-container:hover .banner-content {
          animation-play-state: paused;
        }
      `}</style>
    </div>
  );
};
