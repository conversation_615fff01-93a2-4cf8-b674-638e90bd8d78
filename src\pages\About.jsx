import { useEffect } from "react";
import Slides from "../features/about/components/Advantages/index.jsx";
import Intro from "../features/about/components/Intro/index.jsx";
import ExtenededFooter from "../layout/ExtendedFooter/index.jsx";

const About = () => {
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, []);

  return (
    <div>
      <Intro />
      <Slides />
      <ExtenededFooter />
    </div>
  );
};

export default About;
