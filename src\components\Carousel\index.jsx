import { useState, useRef, useEffect } from "react";
import CarouselCard from "./components/CarouselCard";
import CarouselController from "./components/CarouselController";
import Card from "./../Card/Card";

const Carousel = ({ carouselData, itemsPerIndex = 3, isDark = false }) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const scrollRef = useRef(null);

  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Mobile uses 1 item per index, desktop uses the provided itemsPerIndex
  const currentItemsPerIndex = isMobile ? 1 : itemsPerIndex;

  return (
    <div className="w-full mx-auto">
      {/* Carousel Items */}
      {/* Desktop: Grid layout */}
      <div className="hidden md:grid md:grid-cols-3 gap-6 mb-6">
        {carouselData
          .filter(
            (_, index) =>
              index >= itemsPerIndex * activeIndex &&
              index <= itemsPerIndex * activeIndex + (itemsPerIndex - 1)
          )
          .map((item) => (
            <Card
              key={item.id}
              imgSrc={item.image}
              title={item.title}
              price={item.price}
              subtitle={item.description}
              isDark={isDark}
            />
          ))}
      </div>

      {/* Mobile: Single card with pagination */}
      <div className="md:hidden mb-6">
        <div className="px-4">
          {carouselData
            .filter(
              (_, index) =>
                index >= currentItemsPerIndex * activeIndex &&
                index <=
                  currentItemsPerIndex * activeIndex +
                    (currentItemsPerIndex - 1)
            )
            .map((item) => (
              <Card
                key={item.id}
                imgSrc={item.image}
                title={item.title}
                price={item.price}
                subtitle={item.description}
                isDark={isDark}
              />
            ))}
        </div>
      </div>

      {/* Custom Sliding Controller - Show on both mobile and desktop */}
      <CarouselController
        totalItems={carouselData.length}
        activeIndex={activeIndex}
        onIndexChange={setActiveIndex}
        itemsPerIndex={currentItemsPerIndex}
      />
    </div>
  );
};

export default Carousel;
