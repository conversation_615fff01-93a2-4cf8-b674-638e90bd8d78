// store/destinationsStore.js
import { create } from "zustand";

const useDestinationsStore = create((set) => ({
  destinations: [],
  loading: false,
  error: null,

  fetchDestinations: async (pageSize = 100, pageNumber = 1) => {
    set({ loading: true, error: null });

    try {
      const url = `${import.meta.env.VITE_API_BASE_URL}/api/destinations?pageSize=${pageSize}&pageNumber=${pageNumber}`;
      const res = await fetch(url);

      if (!res.ok) throw new Error(`Error fetching destinations: ${res.status}`);

      const data = await res.json();
      set({
        destinations: data.destinations || [],
        loading: false,
      });
    } catch (err) {
      console.error("❌ Fetch Destinations Error:", err);
      set({
        error: err.message,
        loading: false,
      });
    }
  },
}));

export default useDestinationsStore;
