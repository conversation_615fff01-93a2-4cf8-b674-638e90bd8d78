import { create } from 'zustand';

const useDeveloperStore = create((set) => ({
  developers: [],
  loading: false,
  error: null,

  fetchDevelopers: async () => {
    set({ loading: true, error: null });
    try {
      const res = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/developers`);
      if (!res.ok) {
        throw new Error('Failed to fetch developers');
      }
      const data = await res.json();
    //   console.log(data)
      set({ developers: data.developers || [], loading: false });
    } catch (err) {
      console.error('Failed to fetch developers:', err);
      set({ developers: [], loading: false, error: err.message });
    }
  },
}));

export default useDeveloperStore;
