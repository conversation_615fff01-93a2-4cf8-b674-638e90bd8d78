import { useState } from "react";
import { useLocation } from "react-router-dom";
import styles from "./Header.module.css";
import Logo from "./Logo";
import NavLinks from "./NavLinks";
import CTALink from "./CTALink";
import { HiMenu, HiX } from "react-icons/hi";

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();
  const isHomePage = location.pathname === "/";

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  const navClass = isHomePage ? styles.nav_home : styles.nav;

  return (
    <header className={`${styles.header}`}>
      <div className={styles.inner}>
        {/* Logo */}
        <div className={styles.logo}>
          <Logo />
        </div>

        {/* Desktop Nav */}
        <nav className={`${navClass} ${styles.desktopNav}`}>
          <NavLinks />
        </nav>

        {/* Desktop CTA */}
        <div className={`${styles.cta} ${styles.desktopCta}`}>
          <CTALink />
        </div>

        {/* Mobile Menu Toggle (Hamburger only) */}
        {!isMenuOpen && (
          <button
            className={styles.mobileMenuToggle}
            onClick={toggleMenu}
            aria-label="Open menu"
          >
            <HiMenu />
          </button>
        )}
      </div>

      {/* Mobile Overlay */}
      <div
        className={`${styles.mobileOverlay} ${
          isMenuOpen ? styles.overlayOpen : ""
        }`}
        onClick={closeMenu}
      ></div>

      {/* Mobile Nav */}
      <nav
        className={`${navClass} ${styles.mobileNav} ${
          isMenuOpen ? styles.mobileNavOpen : ""
        }`}
      >
        {/* Sticky Close Button */}
        {isMenuOpen && (
          <button
            className={`${styles.mobileMenuToggle} ${styles.stickyClose}`}
            onClick={toggleMenu}
            aria-label="Close menu"
          >
            <HiX />
          </button>
        )}

        <div className={styles.mobileNavContent}>
          <NavLinks onLinkClick={closeMenu} />
          <div className={styles.mobileCta}>
            <CTALink onLinkClick={closeMenu} />
          </div>
        </div>
      </nav>
    </header>
  );
}
